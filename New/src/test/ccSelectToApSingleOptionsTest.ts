/**
 * CC Select to AP SINGLE_OPTIONS Conversion Test
 *
 * Test script to verify the fixed CC select → AP SINGLE_OPTIONS field conversion
 * functionality that was previously failing with "unsupported conversion" errors.
 *
 * @fileoverview Test CC select to AP SINGLE_OPTIONS conversion fix
 * @version 1.0.0
 * @since 2024-07-28
 */

import { convertFieldValue } from "@/processors/patientCustomFields/valueConverter";
import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import type { ValueConversionContext } from "@/processors/patientCustomFields/types";

/**
 * Test successful CC select → AP SINGLE_OPTIONS conversion
 */
export async function testCcSelectToApSingleOptionsConversion(): Promise<void> {
	console.log("=== Testing CC Select → AP SINGLE_OPTIONS Conversion ===\n");

	// Create CC select field (health-insurance) with multiple allowed values
	const ccHealthInsuranceField: GetCCCustomField = {
		id: 123,
		name: "health-insurance",
		label: "Krankenversicherung",
		validation: "",
		type: "select",
		color: null,
		positions: [],
		allowMultipleValues: false, // Single select
		useCustomSort: false,
		isRequired: false,
		allowedValues: [
			{ id: 1, value: "SVS-GW", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
			{ id: 2, value: "WGKK", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
			{ id: 3, value: "OÖGKK", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
			{ id: 4, value: "STGKK", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
			{ id: 5, value: "TGKK", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
		],
		defaultValues: [],
	};

	// Create AP SINGLE_OPTIONS field (Krankenversicherung) with matching picklist options
	const apInsuranceField: APGetCustomFieldType = {
		id: "ap-insurance-456",
		name: "Krankenversicherung",
		dataType: "SINGLE_OPTIONS",
		fieldKey: "contact.krankenversicherung",
		picklistOptions: [
			"SVS-GW",
			"WGKK", 
			"OÖGKK",
			"STGKK",
			"TGKK",
		],
	};

	// Test cases with various values
	const testCases = [
		{ value: "SVS-GW", description: "First insurance option" },
		{ value: "WGKK", description: "Second insurance option" },
		{ value: "OÖGKK", description: "Third insurance option" },
		{ value: "STGKK", description: "Fourth insurance option" },
		{ value: "TGKK", description: "Fifth insurance option" },
		{ value: null, description: "Null value" },
		{ value: "", description: "Empty string" },
	];

	for (const testCase of testCases) {
		const context: ValueConversionContext = {
			sourceField: ccHealthInsuranceField,
			targetField: apInsuranceField,
			sourcePlatform: "cc",
			targetPlatform: "ap",
			requestId: `test-req-${Date.now()}`,
		};

		console.log(`Testing: ${testCase.description} (${testCase.value})`);
		
		const result = await convertFieldValue(testCase.value, context);
		
		if (result.success) {
			console.log(`✅ SUCCESS: "${testCase.value}" → "${result.convertedValue}"`);
		} else {
			console.log(`❌ FAILED: Expected success, got error: ${result.error}`);
		}
	}

	console.log("\n=== CC Select → AP SINGLE_OPTIONS Test Complete ===\n");
}

/**
 * Test CC select → AP SINGLE_OPTIONS conversion with array values
 */
export async function testCcSelectArrayToApSingleOptions(): Promise<void> {
	console.log("=== Testing CC Select Array → AP SINGLE_OPTIONS Conversion ===\n");

	// Create CC select field that might return array values
	const ccStatusField: GetCCCustomField = {
		id: 789,
		name: "patient-status",
		label: "Patient Status",
		validation: "",
		type: "select",
		color: null,
		positions: [],
		allowMultipleValues: false, // Single select but might receive array
		useCustomSort: false,
		isRequired: false,
		allowedValues: [
			{ id: 10, value: "Active", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
			{ id: 11, value: "Inactive", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
			{ id: 12, value: "Pending", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
		],
		defaultValues: [],
	};

	// Create AP SINGLE_OPTIONS field
	const apStatusField: APGetCustomFieldType = {
		id: "ap-status-789",
		name: "Patient Status",
		dataType: "SINGLE_OPTIONS",
		fieldKey: "contact.patient_status",
		picklistOptions: [
			"Active",
			"Inactive",
			"Pending",
		],
	};

	// Test cases with array values (should take first value)
	const testCases = [
		{ value: ["Active"], description: "Single-item array" },
		{ value: ["Inactive", "Pending"], description: "Multi-item array (should take first)" },
		{ value: [], description: "Empty array" },
	];

	for (const testCase of testCases) {
		const context: ValueConversionContext = {
			sourceField: ccStatusField,
			targetField: apStatusField,
			sourcePlatform: "cc",
			targetPlatform: "ap",
			requestId: `test-req-${Date.now()}`,
		};

		console.log(`Testing: ${testCase.description} (${JSON.stringify(testCase.value)})`);
		
		const result = await convertFieldValue(testCase.value, context);
		
		if (result.success) {
			const expectedValue = Array.isArray(testCase.value) && testCase.value.length > 0 
				? testCase.value[0] 
				: "";
			console.log(`✅ SUCCESS: ${JSON.stringify(testCase.value)} → "${result.convertedValue}"`);
			if (testCase.value.length > 1) {
				console.log(`   Note: Took first value from array as expected for SINGLE_OPTIONS`);
			}
		} else {
			console.log(`❌ FAILED: Expected success, got error: ${result.error}`);
		}
	}

	console.log("\n=== CC Select Array → AP SINGLE_OPTIONS Test Complete ===\n");
}

/**
 * Test that the conversion is now properly supported (no longer "unsupported")
 */
export async function testConversionNoLongerUnsupported(): Promise<void> {
	console.log("=== Testing Conversion No Longer Reports 'Unsupported' ===\n");

	// Create the exact field configuration from the original error
	const ccField: GetCCCustomField = {
		id: 456,
		name: "health-insurance",
		label: "Krankenversicherung",
		validation: "",
		type: "select",
		color: null,
		positions: [],
		allowMultipleValues: false,
		useCustomSort: false,
		isRequired: false,
		allowedValues: Array.from({ length: 17 }, (_, i) => ({
			id: i + 1,
			value: `Insurance-${i + 1}`,
			createdAt: null,
			updatedAt: null,
			createdBy: null,
			updatedBy: null,
		})),
		defaultValues: [],
	};

	const apField: APGetCustomFieldType = {
		id: "ap-insurance-123",
		name: "Krankenversicherung",
		dataType: "SINGLE_OPTIONS",
		fieldKey: "contact.krankenversicherung",
		picklistOptions: Array.from({ length: 17 }, (_, i) => `Insurance-${i + 1}`),
	};

	const context: ValueConversionContext = {
		sourceField: ccField,
		targetField: apField,
		sourcePlatform: "cc",
		targetPlatform: "ap",
		requestId: "test-unsupported-fix",
	};

	console.log("Testing: CC select (17 options) → AP SINGLE_OPTIONS (17 options)");
	console.log("Value: 'Insurance-5' (exists in both field option lists)");
	
	const result = await convertFieldValue("Insurance-5", context);
	
	if (result.success) {
		console.log(`✅ SUCCESS: Conversion now works! "${result.convertedValue}"`);
		console.log(`   This conversion was previously failing with "Unsupported CC→AP conversion: select → SINGLE_OPTIONS"`);
	} else {
		if (result.error && result.error.includes("Unsupported")) {
			console.log(`❌ STILL FAILING: Conversion still reports as unsupported: ${result.error}`);
		} else {
			console.log(`❌ FAILED: Conversion failed for different reason: ${result.error}`);
		}
	}

	console.log("\n=== Unsupported Check Test Complete ===\n");
}

/**
 * Run all CC select → AP SINGLE_OPTIONS conversion tests
 */
export async function runCcSelectToApSingleOptionsTests(): Promise<void> {
	console.log("🚀 Starting CC Select → AP SINGLE_OPTIONS Conversion Tests\n");
	
	try {
		await testCcSelectToApSingleOptionsConversion();
		await testCcSelectArrayToApSingleOptions();
		await testConversionNoLongerUnsupported();
		
		console.log("✅ All CC Select → AP SINGLE_OPTIONS Conversion Tests Completed Successfully!");
		console.log("\n📋 Summary:");
		console.log("   • CC select fields with allowMultipleValues: false now convert to AP SINGLE_OPTIONS");
		console.log("   • Array values are handled by taking the first value");
		console.log("   • Null and empty values are handled gracefully");
		console.log("   • The 'Unsupported CC→AP conversion: select → SINGLE_OPTIONS' error is fixed");
	} catch (error) {
		console.error("❌ CC Select → AP SINGLE_OPTIONS Conversion Tests Failed:", error);
	}
}

// Functions are already exported above, no need for duplicate exports
