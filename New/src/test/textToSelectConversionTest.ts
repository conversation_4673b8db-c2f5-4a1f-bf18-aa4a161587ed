/**
 * TEXT to Select Conversion Test
 *
 * Test script to demonstrate the enhanced AP TEXT → CC select field conversion
 * functionality when text values match allowed select options.
 *
 * @fileoverview Test TEXT to select conversion features
 * @version 1.0.0
 * @since 2024-07-28
 */

import { convertFieldValue } from "@/processors/patientCustomFields/valueConverter";
import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import type { ValueConversionContext } from "@/processors/patientCustomFields/types";

/**
 * Test successful TEXT → select conversion with matching values
 */
export async function testSuccessfulTextToSelectConversion(): Promise<void> {
	console.log("=== Testing Successful TEXT → Select Conversion ===\n");

	// Create AP TEXT field (Gender)
	const apGenderField: APGetCustomFieldType = {
		id: "ap-gender-123",
		name: "Gender",
		dataType: "TEXT",
		fieldKey: "contact.gender",
		picklistOptions: [],
	};

	// Create CC select field (gender) with German options
	const ccGenderField: GetCCCustomField = {
		id: 456,
		name: "gender",
		label: "Geschlecht",
		validation: "",
		type: "select",
		color: null,
		positions: [],
		allowMultipleValues: false,
		useCustomSort: false,
		isRequired: false,
		allowedValues: [
			{ id: 1, value: "männlich", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
			{ id: 2, value: "weiblich", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
			{ id: 3, value: "andere", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
		],
		defaultValues: [],
	};

	// Test cases with matching values
	const testCases = [
		{ value: "weiblich", expected: "weiblich", description: "Female gender" },
		{ value: "männlich", expected: "männlich", description: "Male gender" },
		{ value: "andere", expected: "andere", description: "Other gender" },
	];

	for (const testCase of testCases) {
		const context: ValueConversionContext = {
			sourceField: apGenderField,
			targetField: ccGenderField,
			sourcePlatform: "ap",
			targetPlatform: "cc",
			requestId: `test-req-${Date.now()}`,
		};

		console.log(`Testing: ${testCase.description} (${testCase.value})`);
		
		const result = await convertFieldValue(testCase.value, context);
		
		if (result.success && result.convertedValue === testCase.expected) {
			console.log(`✅ SUCCESS: "${testCase.value}" → "${result.convertedValue}"`);
		} else {
			console.log(`❌ FAILED: Expected "${testCase.expected}", got "${result.convertedValue}"`);
			console.log(`   Error: ${result.error || "Unknown error"}`);
		}
	}

	console.log("\n=== Successful Conversion Test Complete ===\n");
}

/**
 * Test TEXT → select conversion with non-matching values
 */
export async function testNonMatchingTextToSelectConversion(): Promise<void> {
	console.log("=== Testing Non-Matching TEXT → Select Conversion ===\n");

	// Create AP TEXT field
	const apStatusField: APGetCustomFieldType = {
		id: "ap-status-456",
		name: "Patient Status",
		dataType: "TEXT",
		fieldKey: "contact.patient_status",
		picklistOptions: [],
	};

	// Create CC select field with limited options
	const ccStatusField: GetCCCustomField = {
		id: 789,
		name: "status",
		label: "Patient Status",
		validation: "",
		type: "select",
		color: null,
		positions: [],
		allowMultipleValues: false,
		useCustomSort: false,
		isRequired: false,
		allowedValues: [
			{ id: 10, value: "Active", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
			{ id: 11, value: "Inactive", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
			{ id: 12, value: "Pending", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
		],
		defaultValues: [],
	};

	// Test cases with non-matching values
	const testCases = [
		{ value: "Archived", description: "Non-existent status" },
		{ value: "active", description: "Case mismatch (lowercase)" },
		{ value: "ACTIVE", description: "Case mismatch (uppercase)" },
		{ value: "New Patient", description: "Completely different value" },
		{ value: "", description: "Empty string" },
		{ value: null, description: "Null value" },
	];

	for (const testCase of testCases) {
		const context: ValueConversionContext = {
			sourceField: apStatusField,
			targetField: ccStatusField,
			sourcePlatform: "ap",
			targetPlatform: "cc",
			requestId: `test-req-${Date.now()}`,
		};

		console.log(`Testing: ${testCase.description} (${testCase.value})`);
		
		const result = await convertFieldValue(testCase.value, context);
		
		if (testCase.value === "" || testCase.value === null) {
			// Empty/null values should succeed
			if (result.success) {
				console.log(`✅ SUCCESS: "${testCase.value}" → "${result.convertedValue}" (handled gracefully)`);
			} else {
				console.log(`❌ FAILED: Empty/null value should be handled gracefully`);
			}
		} else {
			// Non-matching values should fail gracefully
			if (!result.success) {
				console.log(`✅ SUCCESS: "${testCase.value}" → Conversion skipped (no match found)`);
				console.log(`   Reason: ${result.error}`);
			} else {
				console.log(`❌ UNEXPECTED: "${testCase.value}" → "${result.convertedValue}" (should have been skipped)`);
			}
		}
	}

	console.log("\n=== Non-Matching Conversion Test Complete ===\n");
}

/**
 * Test TEXT → select conversion with field having no allowed values
 */
export async function testTextToSelectWithNoAllowedValues(): Promise<void> {
	console.log("=== Testing TEXT → Select with No Allowed Values ===\n");

	// Create AP TEXT field
	const apNotesField: APGetCustomFieldType = {
		id: "ap-notes-789",
		name: "Notes",
		dataType: "TEXT",
		fieldKey: "contact.notes",
		picklistOptions: [],
	};

	// Create CC select field with no allowed values
	const ccEmptySelectField: GetCCCustomField = {
		id: 999,
		name: "empty_select",
		label: "Empty Select Field",
		validation: "",
		type: "select",
		color: null,
		positions: [],
		allowMultipleValues: false,
		useCustomSort: false,
		isRequired: false,
		allowedValues: [], // No allowed values
		defaultValues: [],
	};

	const context: ValueConversionContext = {
		sourceField: apNotesField,
		targetField: ccEmptySelectField,
		sourcePlatform: "ap",
		targetPlatform: "cc",
		requestId: `test-req-${Date.now()}`,
	};

	console.log("Testing: TEXT field to select field with no allowed values");
	
	const result = await convertFieldValue("Some text value", context);
	
	if (!result.success) {
		console.log(`✅ SUCCESS: Conversion properly skipped for select field with no allowed values`);
		console.log(`   Reason: ${result.error}`);
	} else {
		console.log(`❌ FAILED: Should have skipped conversion for select field with no allowed values`);
	}

	console.log("\n=== No Allowed Values Test Complete ===\n");
}

/**
 * Run all TEXT → select conversion tests
 */
export async function runTextToSelectConversionTests(): Promise<void> {
	console.log("🚀 Starting TEXT → Select Conversion Tests\n");
	
	try {
		await testSuccessfulTextToSelectConversion();
		await testNonMatchingTextToSelectConversion();
		await testTextToSelectWithNoAllowedValues();
		
		console.log("✅ All TEXT → Select Conversion Tests Completed Successfully!");
	} catch (error) {
		console.error("❌ TEXT → Select Conversion Tests Failed:", error);
	}
}

// Functions are already exported above, no need for duplicate exports
