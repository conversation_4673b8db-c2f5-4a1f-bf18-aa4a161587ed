/**
 * Field Matching System
 *
 * Intelligent field matching between AutoPatient and CliniCore custom fields
 * using normalization, similarity scoring, and conflict detection.
 *
 * @fileoverview Custom field matching and conflict resolution
 * @version 1.0.0
 * @since 2024-07-28
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import type { Platform, FieldMatchResult, CustomFieldMapping } from "./types";
import {
	normalizeFieldName,
	normalizedFieldsMatch,
	areFieldNameVariations,
	calculateFieldNameSimilarity
} from "./config/fieldNormalization";
import {
	findMatchingTextboxListField,
	areTextboxListFieldsCompatible
} from "./config/textboxListHandling";
import { logDebug, logInfo, logWarn } from "@/utils/logger";

/**
 * Match AP field with CC fields
 *
 * Attempts to find the best matching CC field for a given AP field using
 * intelligent name matching and type compatibility checking.
 *
 * @param apField - AutoPatient custom field to match
 * @param ccFields - Array of CliniCore custom fields to match against
 * @param existingMappings - Existing field mappings to avoid conflicts
 * @param requestId - Request ID for tracing
 * @returns Field match result with details
 */
export function matchAPFieldWithCC(
	apField: APGetCustomFieldType,
	ccFields: GetCCCustomField[],
	existingMappings: CustomFieldMapping[],
	requestId: string,
): FieldMatchResult {
	logDebug("Matching AP field with CC fields", {
		requestId,
		apFieldId: apField.id,
		apFieldName: apField.name,
		apFieldType: apField.dataType,
		ccFieldCount: ccFields.length,
	});

	// Check if AP field is already mapped
	const existingMapping = existingMappings.find(m => m.apId === apField.id);
	if (existingMapping) {
		const ccField = ccFields.find(f => f.id === existingMapping.ccId);
		if (ccField) {
			logDebug("Found existing mapping for AP field", {
				requestId,
				apFieldId: apField.id,
				ccFieldId: ccField.id,
				mappingId: existingMapping.id,
			});
			return {
				matched: true,
				apField,
				ccField,
				matchType: "exact",
			};
		}
	}

	// Find available CC fields (not already mapped)
	const mappedCCIds = new Set(existingMappings.map(m => m.ccId).filter(Boolean));
	const availableCCFields = ccFields.filter(f => !mappedCCIds.has(f.id));

	if (availableCCFields.length === 0) {
		logWarn("No available CC fields for matching", {
			requestId,
			apFieldId: apField.id,
			totalCCFields: ccFields.length,
			mappedCCFields: mappedCCIds.size,
		});
		return {
			matched: false,
			apField,
			matchType: "none",
			conflictReason: "No available CC fields",
		};
	}

	// Try exact name match first
	const exactMatch = findExactNameMatch(apField, availableCCFields);
	if (exactMatch) {
		const isCompatible = areFieldTypesCompatible(
			apField.dataType as any,
			exactMatch.type as any,
			exactMatch.allowMultipleValues,
		);

		if (isCompatible) {
			logInfo("Found exact name match with compatible types", {
				requestId,
				apFieldId: apField.id,
				ccFieldId: exactMatch.id,
				apFieldName: apField.name,
				ccFieldName: exactMatch.name,
			});
			return {
				matched: true,
				apField,
				ccField: exactMatch,
				matchType: "exact",
			};
		} else {
			logWarn("Exact name match found but types incompatible", {
				requestId,
				apFieldId: apField.id,
				ccFieldId: exactMatch.id,
				apFieldType: apField.dataType,
				ccFieldType: exactMatch.type,
				ccAllowMultiple: exactMatch.allowMultipleValues,
			});
		}
	}

	// Try normalized name match
	const normalizedMatch = findNormalizedNameMatch(apField, availableCCFields);
	if (normalizedMatch) {
		const isCompatible = areFieldTypesCompatible(
			apField.dataType as any,
			normalizedMatch.type as any,
			normalizedMatch.allowMultipleValues,
		);

		if (isCompatible) {
			logInfo("Found normalized name match with compatible types", {
				requestId,
				apFieldId: apField.id,
				ccFieldId: normalizedMatch.id,
				apFieldName: apField.name,
				ccFieldName: normalizedMatch.name,
			});
			return {
				matched: true,
				apField,
				ccField: normalizedMatch,
				matchType: "normalized",
			};
		}
	}

	// Try similarity-based matching
	const similarityMatch = findSimilarityMatch(apField, availableCCFields, 0.8);
	if (similarityMatch) {
		const isCompatible = areFieldTypesCompatible(
			apField.dataType as any,
			similarityMatch.field.type as any,
			similarityMatch.field.allowMultipleValues,
		);

		if (isCompatible) {
			logInfo("Found similarity-based match with compatible types", {
				requestId,
				apFieldId: apField.id,
				ccFieldId: similarityMatch.field.id,
				apFieldName: apField.name,
				ccFieldName: similarityMatch.field.name,
				similarity: similarityMatch.similarity,
			});
			return {
				matched: true,
				apField,
				ccField: similarityMatch.field,
				matchType: "normalized",
			};
		}
	}

	logDebug("No compatible match found for AP field", {
		requestId,
		apFieldId: apField.id,
		apFieldName: apField.name,
		apFieldType: apField.dataType,
		availableFieldCount: availableCCFields.length,
	});

	return {
		matched: false,
		apField,
		matchType: "none",
		conflictReason: "No compatible match found",
	};
}

/**
 * Match CC field with AP fields
 *
 * Attempts to find the best matching AP field for a given CC field.
 *
 * @param ccField - CliniCore custom field to match
 * @param apFields - Array of AutoPatient custom fields to match against
 * @param existingMappings - Existing field mappings to avoid conflicts
 * @param requestId - Request ID for tracing
 * @returns Field match result with details
 */
export function matchCCFieldWithAP(
	ccField: GetCCCustomField,
	apFields: APGetCustomFieldType[],
	existingMappings: CustomFieldMapping[],
	requestId: string,
): FieldMatchResult {
	logDebug("Matching CC field with AP fields", {
		requestId,
		ccFieldId: ccField.id,
		ccFieldName: ccField.name,
		ccFieldType: ccField.type,
		apFieldCount: apFields.length,
	});

	// Check if CC field is already mapped
	const existingMapping = existingMappings.find(m => m.ccId === ccField.id);
	if (existingMapping) {
		const apField = apFields.find(f => f.id === existingMapping.apId);
		if (apField) {
			logDebug("Found existing mapping for CC field", {
				requestId,
				ccFieldId: ccField.id,
				apFieldId: apField.id,
				mappingId: existingMapping.id,
			});
			return {
				matched: true,
				apField,
				ccField,
				matchType: "exact",
			};
		}
	}

	// Find available AP fields (not already mapped)
	const mappedAPIds = new Set(existingMappings.map(m => m.apId).filter(Boolean));
	const availableAPFields = apFields.filter(f => !mappedAPIds.has(f.id));

	if (availableAPFields.length === 0) {
		logWarn("No available AP fields for matching", {
			requestId,
			ccFieldId: ccField.id,
			totalAPFields: apFields.length,
			mappedAPFields: mappedAPIds.size,
		});
		return {
			matched: false,
			ccField,
			matchType: "none",
			conflictReason: "No available AP fields",
		};
	}

	// Try exact name match first
	const exactMatch = findExactNameMatchForCC(ccField, availableAPFields);
	if (exactMatch) {
		const isCompatible = areFieldTypesCompatible(
			exactMatch.dataType as any,
			ccField.type as any,
			ccField.allowMultipleValues,
		);

		if (isCompatible) {
			logInfo("Found exact name match with compatible types", {
				requestId,
				ccFieldId: ccField.id,
				apFieldId: exactMatch.id,
				ccFieldName: ccField.name,
				apFieldName: exactMatch.name,
			});
			return {
				matched: true,
				apField: exactMatch,
				ccField,
				matchType: "exact",
			};
		}
	}

	// Try normalized name match
	const normalizedMatch = findNormalizedNameMatchForCC(ccField, availableAPFields);
	if (normalizedMatch) {
		const isCompatible = areFieldTypesCompatible(
			normalizedMatch.dataType as any,
			ccField.type as any,
			ccField.allowMultipleValues,
		);

		if (isCompatible) {
			logInfo("Found normalized name match with compatible types", {
				requestId,
				ccFieldId: ccField.id,
				apFieldId: normalizedMatch.id,
				ccFieldName: ccField.name,
				apFieldName: normalizedMatch.name,
			});
			return {
				matched: true,
				apField: normalizedMatch,
				ccField,
				matchType: "normalized",
			};
		}
	}

	logDebug("No compatible match found for CC field", {
		requestId,
		ccFieldId: ccField.id,
		ccFieldName: ccField.name,
		ccFieldType: ccField.type,
		availableFieldCount: availableAPFields.length,
	});

	return {
		matched: false,
		ccField,
		matchType: "none",
		conflictReason: "No compatible match found",
	};
}

/**
 * Find exact name match for AP field
 */
function findExactNameMatch(
	apField: APGetCustomFieldType,
	ccFields: GetCCCustomField[],
): GetCCCustomField | undefined {
	return ccFields.find(ccField => 
		ccField.name === apField.name || ccField.label === apField.name
	);
}

/**
 * Find exact name match for CC field
 */
function findExactNameMatchForCC(
	ccField: GetCCCustomField,
	apFields: APGetCustomFieldType[],
): APGetCustomFieldType | undefined {
	return apFields.find(apField => 
		apField.name === ccField.name || apField.name === ccField.label
	);
}

/**
 * Find normalized name match for AP field
 */
function findNormalizedNameMatch(
	apField: APGetCustomFieldType,
	ccFields: GetCCCustomField[],
): GetCCCustomField | undefined {
	return ccFields.find(ccField =>
		normalizedFieldsMatch(apField.name, ccField.name) ||
		normalizedFieldsMatch(apField.name, ccField.label) ||
		areFieldNameVariations(apField.name, ccField.name) ||
		areFieldNameVariations(apField.name, ccField.label)
	);
}

/**
 * Find normalized name match for CC field
 */
function findNormalizedNameMatchForCC(
	ccField: GetCCCustomField,
	apFields: APGetCustomFieldType[],
): APGetCustomFieldType | undefined {
	return apFields.find(apField =>
		normalizedFieldsMatch(ccField.name, apField.name) ||
		normalizedFieldsMatch(ccField.label, apField.name) ||
		areFieldNameVariations(ccField.name, apField.name) ||
		areFieldNameVariations(ccField.label, apField.name)
	);
}

/**
 * Find similarity-based match for AP field
 */
function findSimilarityMatch(
	apField: APGetCustomFieldType,
	ccFields: GetCCCustomField[],
	minSimilarity: number,
): { field: GetCCCustomField; similarity: number } | undefined {
	let bestMatch: { field: GetCCCustomField; similarity: number } | undefined;

	for (const ccField of ccFields) {
		const nameSimilarity = calculateFieldNameSimilarity(apField.name, ccField.name);
		const labelSimilarity = calculateFieldNameSimilarity(apField.name, ccField.label);
		const maxSimilarity = Math.max(nameSimilarity, labelSimilarity);

		if (maxSimilarity >= minSimilarity) {
			if (!bestMatch || maxSimilarity > bestMatch.similarity) {
				bestMatch = { field: ccField, similarity: maxSimilarity };
			}
		}
	}

	return bestMatch;
}
