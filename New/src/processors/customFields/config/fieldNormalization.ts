/**
 * Field Name Normalization Utilities
 *
 * Provides utilities for normalizing field names for comparison between
 * AutoPatient and CliniCore platforms. Handles German umlauts, special
 * characters, spaces, and case differences for improved field matching.
 *
 * @fileoverview Field name normalization utilities for field matching
 * @version 2.0.0
 * @since 2024-07-28
 */

/**
 * German umlaut mappings for normalization
 */
const UMLAUT_MAPPINGS: Record<string, string> = {
	'ä': 'ae',
	'ö': 'oe', 
	'ü': 'ue',
	'Ä': 'Ae',
	'Ö': 'Oe',
	'Ü': 'Ue',
	'ß': 'ss'
};

/**
 * Special character mappings for normalization
 */
const SPECIAL_CHAR_MAPPINGS: Record<string, string> = {
	'-': '_',
	' ': '_',
	'.': '_',
	'/': '_',
	'\\': '_',
	'(': '',
	')': '',
	'[': '',
	']': '',
	'{': '',
	'}': '',
	'<': '',
	'>': '',
	'"': '',
	"'": '',
	'`': '',
	'~': '',
	'!': '',
	'@': '',
	'#': '',
	'$': '',
	'%': '',
	'^': '',
	'&': '',
	'*': '',
	'+': '',
	'=': '',
	'|': '',
	';': '',
	':': '',
	',': '',
	'?': ''
};

/**
 * Normalize field name for comparison
 *
 * Performs comprehensive normalization of field names to improve matching
 * between platforms. Handles German umlauts, special characters, spaces,
 * and case differences.
 *
 * @param fieldName - Field name to normalize
 * @returns Normalized field name
 *
 * @example
 * ```typescript
 * normalizeFieldName("Patient Allergien (Übersicht)"); // "patient_allergien_uebersicht"
 * normalizeFieldName("phone-mobile"); // "phone_mobile"
 * normalizeFieldName("E-Mail Adresse"); // "e_mail_adresse"
 * ```
 */
export function normalizeFieldName(fieldName: string): string {
	if (!fieldName) return '';
	
	let normalized = fieldName;
	
	// Replace German umlauts
	for (const [umlaut, replacement] of Object.entries(UMLAUT_MAPPINGS)) {
		normalized = normalized.replace(new RegExp(umlaut, 'g'), replacement);
	}
	
	// Replace special characters
	for (const [char, replacement] of Object.entries(SPECIAL_CHAR_MAPPINGS)) {
		normalized = normalized.replace(new RegExp(`\\${char}`, 'g'), replacement);
	}
	
	// Convert to lowercase and remove multiple underscores
	normalized = normalized
		.toLowerCase()
		.replace(/_+/g, '_')
		.replace(/^_|_$/g, ''); // Remove leading/trailing underscores
	
	return normalized;
}

/**
 * Check if two field names match after normalization
 *
 * Compares two field names using normalization to handle differences
 * in formatting, special characters, and language variations.
 *
 * @param name1 - First field name
 * @param name2 - Second field name
 * @returns True if names match after normalization
 *
 * @example
 * ```typescript
 * normalizedFieldsMatch("Patient Allergien", "patient-allergien"); // true
 * normalizedFieldsMatch("E-Mail", "email"); // false (different words)
 * normalizedFieldsMatch("Telefon Nummer", "telefon_nummer"); // true
 * ```
 */
export function normalizedFieldsMatch(name1: string, name2: string): boolean {
	if (!name1 || !name2) return false;
	
	const normalized1 = normalizeFieldName(name1);
	const normalized2 = normalizeFieldName(name2);
	
	return normalized1 === normalized2;
}

/**
 * Generate normalized field key for AP fields
 *
 * Creates a normalized field key suitable for AutoPatient field creation.
 * Ensures the key follows AP conventions and is unique.
 *
 * @param fieldName - Source field name
 * @param prefix - Optional prefix (default: "contact")
 * @returns Normalized field key
 *
 * @example
 * ```typescript
 * generateNormalizedFieldKey("Patient Allergien"); // "contact.patient_allergien"
 * generateNormalizedFieldKey("Spezielle Notizen", "custom"); // "custom.spezielle_notizen"
 * ```
 */
export function generateNormalizedFieldKey(
	fieldName: string, 
	prefix: string = "contact"
): string {
	const normalized = normalizeFieldName(fieldName);
	return `${prefix}.${normalized}`;
}

/**
 * Extract field name from AP fieldKey
 *
 * Extracts the field name portion from an AutoPatient fieldKey,
 * removing the prefix and returning the normalized name.
 *
 * @param fieldKey - AP field key (e.g., "contact.patient_allergien")
 * @returns Extracted field name
 *
 * @example
 * ```typescript
 * extractFieldNameFromKey("contact.patient_allergien"); // "patient_allergien"
 * extractFieldNameFromKey("custom.special_notes"); // "special_notes"
 * extractFieldNameFromKey("invalid_key"); // "invalid_key"
 * ```
 */
export function extractFieldNameFromKey(fieldKey: string): string {
	if (!fieldKey) return '';
	
	const parts = fieldKey.split('.');
	return parts.length > 1 ? parts[parts.length - 1] : fieldKey;
}

/**
 * Common field name variations for improved matching
 */
export const FIELD_NAME_VARIATIONS: Record<string, string[]> = {
	'phone': ['telefon', 'telephone', 'tel', 'phone_number', 'telefonnummer'],
	'mobile': ['handy', 'mobiltelefon', 'mobile_phone', 'cell', 'cellular'],
	'email': ['e_mail', 'mail', 'email_address', 'e_mail_adresse'],
	'address': ['adresse', 'anschrift', 'wohnort', 'strasse'],
	'name': ['vorname', 'nachname', 'fullname', 'complete_name'],
	'birthday': ['geburtstag', 'geburtsdatum', 'birth_date', 'date_of_birth'],
	'gender': ['geschlecht', 'sex'],
	'notes': ['notizen', 'bemerkungen', 'comments', 'anmerkungen'],
	'allergies': ['allergien', 'allergie', 'allergy'],
	'insurance': ['versicherung', 'krankenkasse', 'insurance_company']
};

/**
 * Check if field names are variations of the same concept
 *
 * Uses predefined variations to check if two field names refer to
 * the same concept despite different naming conventions.
 *
 * @param name1 - First field name
 * @param name2 - Second field name
 * @returns True if names are variations of the same concept
 *
 * @example
 * ```typescript
 * areFieldNameVariations("phone", "telefon"); // true
 * areFieldNameVariations("email", "e_mail"); // true
 * areFieldNameVariations("notes", "comments"); // false (not in variations)
 * ```
 */
export function areFieldNameVariations(name1: string, name2: string): boolean {
	const normalized1 = normalizeFieldName(name1);
	const normalized2 = normalizeFieldName(name2);
	
	// Check if they're the same after normalization
	if (normalized1 === normalized2) return true;
	
	// Check variations
	for (const [concept, variations] of Object.entries(FIELD_NAME_VARIATIONS)) {
		const allVariations = [concept, ...variations];
		const hasName1 = allVariations.some(variation => 
			normalizeFieldName(variation) === normalized1
		);
		const hasName2 = allVariations.some(variation => 
			normalizeFieldName(variation) === normalized2
		);
		
		if (hasName1 && hasName2) return true;
	}
	
	return false;
}

/**
 * Similarity score between two normalized field names
 *
 * Calculates a similarity score (0-1) between two field names using
 * Levenshtein distance and normalization.
 *
 * @param name1 - First field name
 * @param name2 - Second field name
 * @returns Similarity score (0-1, where 1 is identical)
 */
export function calculateFieldNameSimilarity(name1: string, name2: string): number {
	const normalized1 = normalizeFieldName(name1);
	const normalized2 = normalizeFieldName(name2);
	
	if (normalized1 === normalized2) return 1.0;
	if (!normalized1 || !normalized2) return 0.0;
	
	// Simple Levenshtein distance calculation
	const matrix: number[][] = [];
	const len1 = normalized1.length;
	const len2 = normalized2.length;
	
	// Initialize matrix
	for (let i = 0; i <= len1; i++) {
		matrix[i] = [i];
	}
	for (let j = 0; j <= len2; j++) {
		matrix[0][j] = j;
	}
	
	// Fill matrix
	for (let i = 1; i <= len1; i++) {
		for (let j = 1; j <= len2; j++) {
			const cost = normalized1[i - 1] === normalized2[j - 1] ? 0 : 1;
			matrix[i][j] = Math.min(
				matrix[i - 1][j] + 1,      // deletion
				matrix[i][j - 1] + 1,      // insertion
				matrix[i - 1][j - 1] + cost // substitution
			);
		}
	}
	
	const distance = matrix[len1][len2];
	const maxLength = Math.max(len1, len2);
	
	return maxLength === 0 ? 1.0 : 1.0 - (distance / maxLength);
}
