/**
 * CliniCore Field Conversion Utilities
 *
 * Provides specialized utilities for converting CliniCore custom fields
 * to AutoPatient format with comprehensive type mapping, multi-value field
 * handling, and TEXTBOX_LIST support.
 *
 * @fileoverview CC-specific field conversion utilities
 * @version 2.0.0
 * @since 2024-07-28
 */

import type { APPostCustomfieldType, GetCCCustomField } from "@type";
import { logCustomField, logFieldConversionFailure } from "@/utils/logger";
import { 
	getCcToApMapping, 
	BOOLEAN_RADIO_OPTIONS,
	type CCFieldType 
} from "../config/fieldTypeMappings";

/**
 * Convert CliniCore custom field to AutoPatient format
 *
 * Performs intelligent field type conversion with bidirectional compatibility.
 * Handles edge cases gracefully and provides detailed logging for traceability.
 * Uses centralized mapping configuration with allowMultipleValues logic.
 *
 * @param ccField - CliniCore custom field object to convert
 * @param customName - Optional custom name for the AP field
 * @param customFieldKey - Optional custom field key for the AP field
 * @returns AutoPatient custom field format ready for API submission
 *
 * @example
 * ```typescript
 * // Convert CC multi-value text to AP TEXTBOX_LIST
 * const ccTextField: GetCCCustomField = {
 *   id: 1,
 *   name: "allergies",
 *   label: "Patient Allergies",
 *   type: "text",
 *   allowMultipleValues: true
 * };
 *
 * const apField = convertCcFieldToAp(ccTextField);
 * // Result: { dataType: "TEXTBOX_LIST", ... }
 *
 * // Convert CC boolean to AP RADIO with Yes/Ja, No/Nein options
 * const ccBooleanField: GetCCCustomField = {
 *   id: 2,
 *   name: "newsletter",
 *   label: "Newsletter Subscription",
 *   type: "boolean"
 * };
 *
 * const apRadioField = convertCcFieldToAp(ccBooleanField);
 * // Result: { dataType: "RADIO", options: ["Yes", "Ja", "No", "Nein"], ... }
 * ```
 */
export function convertCcFieldToAp(
	ccField: GetCCCustomField,
	customName?: string,
	customFieldKey?: string,
): APPostCustomfieldType {
	const fieldType = ccField.type as CCFieldType;
	
	logCustomField("CC→AP conversion started", ccField.name, {
		ccFieldType: fieldType,
		allowMultiple: ccField.allowMultipleValues,
		hasAllowedValues: Boolean(ccField.allowedValues?.length),
		valueCount: ccField.allowedValues?.length || 0,
	});

	// Base field structure with common properties
	const baseField: APPostCustomfieldType = {
		name: customName || ccField.label || ccField.name,
		dataType: "TEXT", // Default fallback type
		placeholder: "",
		...(customFieldKey && { fieldKey: customFieldKey }),
	};

	// Use centralized mapping configuration with allowMultipleValues logic
	switch (fieldType) {
		case "text":
			return convertCcTextField(ccField, baseField);
		case "textarea":
			return convertCcTextareaField(ccField, baseField);
		case "number":
			return convertCcNumberField(ccField, baseField);
		case "telephone":
			return convertCcTelephoneField(ccField, baseField);
		case "email":
			return convertCcEmailField(ccField, baseField);
		case "date":
			return convertCcDateField(ccField, baseField);
		case "select":
			return convertCcSelectField(ccField, baseField);
		case "select-or-custom":
			return convertCcSelectOrCustomField(ccField, baseField);
		case "boolean":
			return convertCcBooleanField(ccField, baseField);
		default:
			return convertCcFallbackField(ccField, baseField, fieldType);
	}
}

/**
 * Convert CC text field with multi-value support
 */
function convertCcTextField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	// Check if multi-value text should become TEXTBOX_LIST
	if (ccField.allowMultipleValues) {
		logCustomField("CC→AP text(multi)→TEXTBOX_LIST conversion", ccField.name, {
			allowMultiple: true,
			convertedType: "TEXTBOX_LIST"
		});
		return {
			...baseField,
			dataType: "TEXTBOX_LIST",
		};
	}

	logCustomField("CC→AP text→TEXT conversion", ccField.name);
	return {
		...baseField,
		dataType: "TEXT",
	};
}

/**
 * Convert CC textarea field with multi-value support
 */
function convertCcTextareaField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	// Check if multi-value textarea should become TEXTBOX_LIST
	if (ccField.allowMultipleValues) {
		logCustomField("CC→AP textarea(multi)→TEXTBOX_LIST conversion", ccField.name, {
			allowMultiple: true,
			convertedType: "TEXTBOX_LIST"
		});
		return {
			...baseField,
			dataType: "TEXTBOX_LIST",
		};
	}

	logCustomField("CC→AP textarea→LARGE_TEXT conversion", ccField.name);
	return {
		...baseField,
		dataType: "LARGE_TEXT",
	};
}

/**
 * Convert CC number field with multi-value support
 */
function convertCcNumberField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	// Check if multi-value number should become TEXTBOX_LIST
	if (ccField.allowMultipleValues) {
		logCustomField("CC→AP number(multi)→TEXTBOX_LIST conversion", ccField.name, {
			allowMultiple: true,
			convertedType: "TEXTBOX_LIST"
		});
		return {
			...baseField,
			dataType: "TEXTBOX_LIST",
		};
	}

	logCustomField("CC→AP number→NUMERICAL conversion", ccField.name);
	return {
		...baseField,
		dataType: "NUMERICAL",
	};
}

/**
 * Convert CC telephone field with multi-value support
 */
function convertCcTelephoneField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	// Check if multi-value telephone should become TEXTBOX_LIST
	if (ccField.allowMultipleValues) {
		logCustomField("CC→AP telephone(multi)→TEXTBOX_LIST conversion", ccField.name, {
			allowMultiple: true,
			convertedType: "TEXTBOX_LIST"
		});
		return {
			...baseField,
			dataType: "TEXTBOX_LIST",
		};
	}

	logCustomField("CC→AP telephone→PHONE conversion", ccField.name);
	return {
		...baseField,
		dataType: "PHONE",
	};
}

/**
 * Convert CC email field with multi-value support
 */
function convertCcEmailField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	// Check if multi-value email should become TEXTBOX_LIST
	if (ccField.allowMultipleValues) {
		logCustomField("CC→AP email(multi)→TEXTBOX_LIST conversion", ccField.name, {
			allowMultiple: true,
			convertedType: "TEXTBOX_LIST"
		});
		return {
			...baseField,
			dataType: "TEXTBOX_LIST",
		};
	}

	logCustomField("CC→AP email→EMAIL conversion", ccField.name);
	return {
		...baseField,
		dataType: "EMAIL",
	};
}

/**
 * Convert CC date field to AP DATE
 */
function convertCcDateField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	logCustomField("CC→AP date→DATE conversion", ccField.name);
	return {
		...baseField,
		dataType: "DATE",
	};
}

/**
 * Convert CC boolean field with Yes/Ja, No/Nein options
 */
function convertCcBooleanField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	logCustomField("CC→AP boolean→RADIO conversion", ccField.name, {
		options: BOOLEAN_RADIO_OPTIONS.MIXED,
		reversibleConversion: true,
	});

	return {
		...baseField,
		dataType: "RADIO",
		options: BOOLEAN_RADIO_OPTIONS.MIXED,
	};
}

/**
 * Convert CC select-or-custom field to AP SINGLE_OPTIONS
 */
function convertCcSelectOrCustomField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	const options = extractOptionsFromAllowedValues(ccField.allowedValues);

	logCustomField("CC→AP select-or-custom→SINGLE_OPTIONS conversion", ccField.name, {
		optionCount: options.length,
		customOptionsAllowed: true,
	});

	return {
		...baseField,
		dataType: "SINGLE_OPTIONS",
		options: options,
	};
}

/**
 * Convert CC select field based on allowMultipleValues
 */
function convertCcSelectField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	const options = extractOptionsFromAllowedValues(ccField.allowedValues);

	if (ccField.allowMultipleValues) {
		logCustomField("CC→AP select→MULTIPLE_OPTIONS conversion", ccField.name, {
			optionCount: options.length,
			allowMultiple: true,
		});

		return {
			...baseField,
			dataType: "MULTIPLE_OPTIONS",
			options: options,
		};
	}

	logCustomField("CC→AP select→SINGLE_OPTIONS conversion", ccField.name, {
		optionCount: options.length,
		allowMultiple: false,
	});

	return {
		...baseField,
		dataType: "SINGLE_OPTIONS",
		options: options,
	};
}

/**
 * Convert unmapped CC field types with fallback to TEXT
 */
function convertCcFallbackField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
	fieldType: CCFieldType,
): APPostCustomfieldType {
	logFieldConversionFailure(
		"CC→AP",
		{
			id: ccField.id,
			name: ccField.name,
			type: fieldType,
			allowMultipleValues: ccField.allowMultipleValues,
			allowedValues: ccField.allowedValues,
		},
		{
			id: "fallback",
			name: "text_fallback",
			type: "TEXT",
		},
		null,
		`Unsupported CC→AP conversion: ${fieldType} → TEXT (using fallback)`,
	);

	return {
		...baseField,
		dataType: "TEXT",
	};
}

/**
 * Extract option values from CC allowedValues array
 */
function extractOptionsFromAllowedValues(
	allowedValues: GetCCCustomField["allowedValues"],
): string[] {
	if (!allowedValues || !Array.isArray(allowedValues)) {
		return [];
	}

	return allowedValues
		.map((item) => item.value)
		.filter(
			(value): value is string => typeof value === "string" && value.length > 0,
		);
}
