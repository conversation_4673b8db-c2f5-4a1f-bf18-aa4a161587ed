/**
 * AutoPatient to CliniCore Custom Field Conversion Utility
 *
 * Transforms AutoPatient custom field structures to CliniCore format with
 * bidirectional compatibility, comprehensive logging, and graceful error handling.
 *
 * **Key Conversion Rules (Updated v2.0):**
 * - TEXT → text
 * - LARGE_TEXT → textarea
 * - NUMERICAL → number
 * - <PERSON><PERSON><PERSON><PERSON> → telephone
 * - MONETORY → text
 * - CHECKBOX → select (allowMultipleValues: true)
 * - SINGLE_OPTIONS → select (allowMultipleValues: false)
 * - MULTIPLE_OPTIONS → select (allowMultipleValues: true)
 * - DATE → date
 * - RADIO → select (allowMultipleValues: false) OR boolean (Yes/Ja, No/Nein)
 * - EMAIL → email
 * - TEXTBOX_LIST → text (allowMultipleValues: true)
 * - FILE_UPLOAD → Skip entirely (do not sync)
 *
 * **Features:**
 * - Strict TypeScript compliance (no `any` usage)
 * - Reversible transformations for data integrity
 * - Comprehensive error handling with request ID tracing
 * - Performance-optimized for bulk conversions
 * - Detailed logging for debugging and monitoring
 *
 * @since 1.0.0
 * @version 2.0.0
 */

import type { APGetCustomFieldType, PostCCCustomField } from "@type";
import { logCustomField, logWarn, logFieldConversionFailure } from "@/utils/logger";
import {
	getApToCcMapping,
	shouldSkipApField,
	shouldConvertRadioToBoolean,
	type APFieldType
} from "./config/fieldTypeMappings";

/**
 * Convert AutoPatient custom field to CliniCore format
 *
 * Performs intelligent field type conversion with bidirectional compatibility.
 * Handles edge cases gracefully and provides detailed logging for traceability.
 * Uses centralized mapping configuration for consistent conversions.
 *
 * @param apField - AutoPatient custom field object to convert
 * @returns CliniCore custom field format ready for API submission or null if should be skipped
 *
 * @example
 * ```typescript
 * // Convert AP TEXTBOX_LIST to CC multi-value text
 * const apTextboxField: APGetCustomFieldType = {
 *   id: "field123",
 *   name: "Patient Allergies",
 *   dataType: "TEXTBOX_LIST"
 * };
 *
 * const ccField = apToCcCustomFieldConvert(apTextboxField);
 * // Result: { type: "text", allowMultipleValues: true, ... }
 *
 * // FILE_UPLOAD fields are skipped
 * const apFileField: APGetCustomFieldType = {
 *   id: "field456",
 *   name: "Document Upload",
 *   dataType: "FILE_UPLOAD"
 * };
 *
 * const skippedField = apToCcCustomFieldConvert(apFileField);
 * // Result: null (field is skipped)
 * ```
 */
export function apToCcCustomFieldConvert(
	apField: APGetCustomFieldType,
): PostCCCustomField | null {
	const fieldType = apField.dataType as APFieldType;

	// Check if field should be skipped entirely
	if (shouldSkipApField(fieldType)) {
		logCustomField("AP→CC field skipped", apField.name, {
			apFieldType: fieldType,
			reason: "FILE_UPLOAD fields are not synced to CC",
		});
		return null;
	}

	logCustomField("AP→CC conversion started", apField.name, {
		apFieldType: fieldType,
		hasOptions: Boolean(apField.picklistOptions?.length),
		optionCount: apField.picklistOptions?.length || 0,
	});

	// Base field structure with common properties
	const baseField: PostCCCustomField = {
		name: apField.name,
		label: apField.name, // Use name as label by default
		type: "text", // Default fallback type
		validation: "{}",
		isRequired: false,
		allowMultipleValues: false,
	};

	// Use centralized mapping configuration
	switch (fieldType) {
		case "TEXT":
			return handleApTextField(apField, baseField);
		case "LARGE_TEXT":
			return handleApLargeTextField(apField, baseField);
		case "NUMERICAL":
			return handleApNumericalField(apField, baseField);
		case "PHONE":
			return handleApPhoneField(apField, baseField);
		case "MONETORY":
			return handleApMonetoryField(apField, baseField);
		case "CHECKBOX":
			return handleApCheckboxField(apField, baseField);
		case "SINGLE_OPTIONS":
			return handleApSingleOptionsField(apField, baseField);
		case "MULTIPLE_OPTIONS":
			return handleApMultipleOptionsField(apField, baseField);
		case "DATE":
			return handleApDateField(apField, baseField);
		case "RADIO":
			return handleApRadioField(apField, baseField);
		case "EMAIL":
			return handleApEmailField(apField, baseField);
		case "TEXTBOX_LIST":
			return handleApTextboxListField(apField, baseField);
		default:
			return handleApFallbackField(apField, baseField, fieldType);
	}
}

/**
 * Handle AP TEXT field conversion
 */
function handleApTextField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	logCustomField("AP→CC TEXT conversion", apField.name);
	return {
		...baseField,
		type: "text",
	};
}

/**
 * Handle AP LARGE_TEXT field conversion
 */
function handleApLargeTextField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	logCustomField("AP→CC LARGE_TEXT→textarea conversion", apField.name);
	return {
		...baseField,
		type: "textarea",
	};
}

/**
 * Handle AP NUMERICAL field conversion
 */
function handleApNumericalField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	logCustomField("AP→CC NUMERICAL→number conversion", apField.name);
	return {
		...baseField,
		type: "number",
	};
}

/**
 * Handle AP PHONE field conversion
 */
function handleApPhoneField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	logCustomField("AP→CC PHONE→telephone conversion", apField.name);
	return {
		...baseField,
		type: "telephone",
	};
}

/**
 * Handle AP MONETORY field conversion
 */
function handleApMonetoryField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	logCustomField("AP→CC MONETORY→text conversion", apField.name, {
		note: "Monetary values stored as text in CC"
	});
	return {
		...baseField,
		type: "text",
	};
}

/**
 * Handle AP CHECKBOX field conversion
 */
function handleApCheckboxField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	const options = apField.picklistOptions || [];
	logCustomField("AP→CC CHECKBOX→select conversion", apField.name, {
		allowMultiple: true,
		optionCount: options.length
	});
	return {
		...baseField,
		type: "select",
		allowMultipleValues: true,
		allowedValues: options.map((option: string) => ({ value: option })),
	};
}

/**
 * Handle AP DATE field conversion
 */
function handleApDateField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	logCustomField("AP→CC DATE→date conversion", apField.name);
	return {
		...baseField,
		type: "date",
	};
}

/**
 * Handle AP EMAIL field conversion
 */
function handleApEmailField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	logCustomField("AP→CC EMAIL→email conversion", apField.name);
	return {
		...baseField,
		type: "email",
	};
}

/**
 * Handle AP TEXTBOX_LIST field conversion
 */
function handleApTextboxListField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	logCustomField("AP→CC TEXTBOX_LIST→text(multi) conversion", apField.name, {
		allowMultiple: true,
		note: "Multi-value text field"
	});
	return {
		...baseField,
		type: "text",
		allowMultipleValues: true,
	};
}

/**
 * Handle AP RADIO field conversion with intelligent boolean detection
 */
function handleApRadioField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	const options = apField.picklistOptions || [];

	// Check if this should be converted to boolean (Yes/Ja, No/Nein pattern)
	if (shouldConvertRadioToBoolean(options)) {
		logCustomField("AP→CC RADIO→boolean conversion", apField.name, {
			detectedAsBoolean: true,
			originalOptions: options,
			conversionReason: "yes_no_pattern_detected",
		});

		return {
			...baseField,
			type: "boolean",
		};
	}

	// Convert to select field for other radio fields
	logCustomField("AP→CC RADIO→select conversion", apField.name, {
		optionCount: options.length,
		options: options,
		conversionReason: options.length === 0 ? "no_options" : "non_boolean_pattern",
	});

	return {
		...baseField,
		type: "select",
		allowMultipleValues: false,
		allowedValues: options.map((option: string) => ({ value: option })),
	};
}

/**
 * Handle AP MULTIPLE_OPTIONS field conversion
 */
function handleApMultipleOptionsField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	const options = apField.picklistOptions || [];

	logCustomField("AP→CC MULTIPLE_OPTIONS→select conversion", apField.name, {
		optionCount: options.length,
		allowMultiple: true,
	});

	return {
		...baseField,
		type: "select",
		allowMultipleValues: true,
		allowedValues: options.map((option: string) => ({ value: option })),
	};
}

/**
 * Handle AP SINGLE_OPTIONS field conversion
 */
function handleApSingleOptionsField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	const options = apField.picklistOptions || [];

	logCustomField("AP→CC SINGLE_OPTIONS→select conversion", apField.name, {
		optionCount: options.length,
		allowMultiple: false,
	});

	return {
		...baseField,
		type: "select",
		allowMultipleValues: false,
		allowedValues: options.map((option: string) => ({ value: option })),
	};
}

/**
 * Handle fallback conversion for unmapped field types
 */
function handleApFallbackField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
	fieldType: APFieldType,
): PostCCCustomField {
	logFieldConversionFailure(
		"AP→CC",
		{
			id: apField.id,
			name: apField.name,
			type: fieldType,
			fieldKey: apField.fieldKey,
			picklistOptions: apField.picklistOptions,
		},
		{
			id: "fallback",
			name: "text_fallback",
			type: "text",
		},
		null,
		`Unsupported AP→CC conversion: ${fieldType} → text (using fallback)`,
	);

	return {
		...baseField,
		type: "text",
	};
}

export default apToCcCustomFieldConvert;
